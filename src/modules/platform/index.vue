<template>
  <div class="platform-container" v-if="isShow" :class="{ 'zoom-out': isZoomOut }">
    <img class="lash-img" :class="{ 'height-100': showThemeTab }" :src="splashImg" alt />
    <div class="content-wrap">
      <span class="text">昨日登录人数</span>
      <div class="flex_center">
        <VueCountUp :endVal="peopleNum" :decimals="0" :duration="500" />
        <span class="text2">人</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex'
import requestMixins from 'common/mixins/requestMixins'
import VueCountUp from 'common/components/vue-count-to/vue-countTo'
// import { themeDetails } from './config/testData.js' // 开发测试用

export default {
  name: 'advertisement',
  mixins: [requestMixins],
  components: { VueCountUp },
  data() {
    return {
      isZoomOut: false,
      isShow: true,
      isTrue: false,
      img1: require(`./img/flash2.png`),
      // img2: require(`./img/chuxi-1080-1920-1.png`),
      // img3: require(`./img/cunjie-1080-1920-1.png`),
      // img4: require(`./img/kaigong-1080-1920-1.png`),
      areaCode: [],
      provinceCode: [],
      bigCode: [],
      peopleNum: 0
    }
  },
  computed: {
    ...mapState(['isDev', 'userMsg', 'showThemeTab']),
    userNo() {
      return sessionStorage.getItem('userId') || ''
    },
    splashImg() {
      // if (this.showThemeTab) {
      //   const day = this.$moment().format('YYYYMMDD')
      //   if (['20230121'].includes(day)) {
      //     return this.img2
      //   }
      //   if (['20230122', '20230123', '20230124', '20230125', '20230126', '20230127'].includes(day)) {
      //     return this.img3
      //   }
      //   if (['20230128', '20230129'].includes(day)) {
      //     return this.img4
      //   }
      // }
      return this.img1
    }
  },

  async created() {
    await this.summaryActiveData()
    await this.initThemeData()
    setTimeout(async () => {
      if (this.isTrue) {
        await this.initUserAuth()
      }
    }, 500)
  },
  methods: {
    ...mapMutations(['setThemeArray', 'setOrgArray', 'setSXMonitor', 'setZoneParams', 'setUserData', 'setAuthList']),
    // 访问人数
    async summaryActiveData() {
      const date = this.$moment().subtract(1, 'days').format('YYYYMMDD')
      const conditionList = {
        start_day: date,
        end_day: date
      }
      const { obj } = await this.sendTwoDimenRequest(
        'ads_mkt_page_click_active_sum_di',
        this.forMapData(conditionList),
        false,
        false
      )
      const data = obj.find(item => item.btn_code === 'SXZK') || {}
      this.peopleNum = data.active_qty || 0
    },
    async initThemeData() {
      const { obj } = await this._getThemeDataAuth()
      // 没有主题模块跳转
      if (!obj.length) {
        this.isTrue = false
        this.$router.push({ name: 'noRights' })
        return
      }

      // 是否有顺心战况的主题项
      const sxTheme = obj.find(item => item.themeId === '5ffbc326967711eaadbdd4ae52789958')
      // 获取父主题是顺心战况的子主题数据
      let themeArray = obj
        // .filter(item => item.parentId === '5ffbc326967711eaadbdd4ae52789958')
        .filter(item => /sxzk/.test(item.themeCode))
        .map(el => {
          return {
            themeName: el.themeName,
            parentId: el.parentId,
            themeId: el.themeId,
            themeCode: el.themeCode
          }
        })
      // 没有顺心模块跳转
      if (!sxTheme) {
        this.isTrue = false
        this.$router.push({ name: 'noRights' })
        return
      }

      if (!themeArray.length) {
        this.isTrue = false
        this.$router.push({ name: 'noRights' })
        return
      }
      // 有顺心的模块
      if (themeArray.length) {
        this.isTrue = true
        // 排序接口
        const { obj: menuObj } = await this._getModulesData()
        if (menuObj && menuObj.menu_content) {
          const temp = JSON.parse(menuObj.menu_content)
          const menu = temp.map(item => {
            // if (item === '实时战况' || item === '高峰战况') {
            //   item = '实时'
            // }
            return item
          })
          // 根据接口数据进行模块排序排序
          themeArray = this.$objectSortUp(themeArray, 'themeName', menu)
        }
        // 所有的主题模块名称
        this.setThemeArray(themeArray)
        sessionStorage.setItem('themeArray', JSON.stringify(themeArray))
      }
    },
    // 获取基础的人员层级权限&&层级数据
    async initUserAuth() {
      // 获取顺心的组织架构树
      const { obj: orgArray } = await this._getOrgArray()
      // console.log(JSON.parse(JSON.stringify(orgArray)), 'orgArray')
      this.setOrgArray(orgArray)
      // 省区
      this.provinceCode = Array.from([...new Set(orgArray.map(item => item.province_area_code))])
      // 区域
      this.areaCode = Array.from([...new Set(orgArray.map(item => item.area_code))])

      const { obj: userObj } = await this._getUserDeptCode()
      let zoneData = {
        zoneLevel: '',
        zoneCode: '',
        zoneName: ''
      }
      if (userObj) {
        const { deptCode } = userObj
        let { typeLevel } = userObj
        if (/^SFC/.test(deptCode) || deptCode === 'TKJ7551' || this.userNo === '545588') {
          zoneData = {
            zoneLevel: 0,
            zoneCode: '001',
            zoneName: '顺心捷达'
          }
        } else {
          let zoneName = ''
          // 省区
          const zoneNameItem = orgArray.find(item => item.province_area_code === deptCode)
          // 区域
          const zoneNameItem2 = orgArray.find(item => item.area_code === deptCode)
          if (zoneNameItem) {
            zoneName = zoneNameItem.province_area_name
          } else if (zoneNameItem2) {
            zoneName = zoneNameItem2.area_name
          }
          if (this.areaCode.includes(deptCode)) {
            typeLevel = 3
          } else if (this.provinceCode.includes(deptCode)) {
            typeLevel = 2
          }
          zoneData = {
            zoneLevel: typeLevel,
            zoneCode: deptCode,
            zoneName
          }
        }
      }

      // 映射数据
      // const { obj: ysAuthRes } = await this._getYsAuthData(zoneData.zoneCode)
      const { obj: ysAuthRes } = await this._getYsAuthMoreData(this.userNo)
      // console.log(ysAuthMoreRes, 'ysAuthMoreRes')
      // const ysAuthRes = [
      //   {
      //     dept_code: 'S551Y022',
      //     data_permission_name: '安徽省区',
      //     dept_name: '安徽省区',
      //     data_permission_code: 'S551Y022',
      //     managed_dept_level: '32'
      //   },
      //   {
      //     dept_code: 'S551S010Y055Y022',
      //     data_permission_name: '河北省区',
      //     dept_name: '河北省区',
      //     data_permission_code: 'S010Y055',
      //     managed_dept_level: '32',
      //     big_area_code: 'S010Y055',
      //     big_area_name: '河北省区',
      //     ky_hq_code: 'S010Y055',
      //     ky_hq_name: '河北省区',
      //     zone_level: 2
      //   }
      // ]
      // const ysAuthRes = [
      // {
      //   area_name: 'SX台州区域,SX温州区域,SX金华区域',
      //   parent_name: '顺心捷达',
      //   emp_code: 'sx21080064',
      //   province_area_name: '浙南省区',
      //   parent_org_level: '30',
      //   area_code: 'S579Y073,S576Y001,S579Y005',
      //   dept_name: '浙南省区',
      //   employee_name: '何家友',
      //   org_category: 'QBZN_1',
      //   site_code: 'S577WK,S579WJ,576WH,577WH,579WH,S570W,S576WK,S577WJ,S577WA',
      //   org_category_name: '区部一级职能',
      //   site_name:
      //     '【SX】苍南接驳点,金华大件中转场,【SX】金华枢纽,【SX】台州中转场,【SX】温州中转场,台州大件中转场,【SX】滨海接驳点,温州大件中转场,【SX】衢州接驳点',
      //   org_biz_type: '30',
      //   province_area_code: 'SCN31',
      //   org_level: '32',
      //   parent_code: '001',
      //   org_code: 'SCN31'
      // },
      // {
      //   area_name: 'SX常州区域,SX苏通区域,SX无锡区域',
      //   parent_name: '顺心捷达',
      //   emp_code: 'sx21080064',
      //   province_area_name: '苏南省区',
      //   parent_org_level: '30',
      //   area_code: 'S512Y080,S510Y096,S519Y002',
      //   dept_name: '苏南省区',
      //   employee_name: '何家友',
      //   org_category: 'QBZN_1',
      //   site_code: 'S512W,S510W,510WH,S519WA,519WH,513WH,S510WA,S513WJ',
      //   org_category_name: '区部一级职能',
      //   site_name:
      //     '南通大件中转场,【SX】无锡枢纽,常州大件中转场,【SX】吴江集配站,【SX】宜兴接驳点,无锡大件中转场,【SX】南通中转场（待撤销）,【SX】常州中转场',
      //   org_biz_type: '30',
      //   province_area_code: 'SCN20',
      //   org_level: '32',
      //   parent_code: '001',
      //   org_code: 'SCN20'
      // },
      // {
      //   area_name: 'SX淮安区域,SX扬泰区域,SX徐宿区域',
      //   parent_name: '浙南省区',
      //   emp_code: 'sx21080064',
      //   province_area_name: '浙南省区',
      //   parent_org_level: '30',
      //   area_code: 'S517Y005,S527Y001,SCN0461',
      //   dept_name: 'sx金华区域',
      //   employee_name: '何家友',
      //   org_category: 'QBZN_1',
      //   site_code:
      //     'S518WB,514WH,523TD,523TC,515VH,S518WA,523TA,S517WJ,514W,S518W,523TH,S527WJ,516EJ,516VH,S523WJ,516WH,517WH',
      //   org_category_name: '区部一级职能',
      //   site_name:
      //     '【SX】泰州中转场,徐州大件中转场,徐州沙集大件集分站,扬州维扬中转场,【SX】连云港接驳点,戴南集配站,姜堰集配站,黄桥集配站,盐城大件集分站,海陵集配站,【SX】赣榆接驳点,【SX】徐州沙集中转场,丰县集配站,【SX】淮安中转场,淮安大件中转场,扬州大件中转场,【SX】连云港海州接驳点',
      //   org_biz_type: '30',
      //   province_area_code: 'SCN31',
      //   org_level: '33',
      //   parent_code: 'SCN31',
      //   org_code: 'S579Y005'
      // }
      // ]

      if (!ysAuthRes.length) {
        const data = {
          user: zoneData,
          zoneParams: zoneData
        }
        this.setSXMonitor(data)
        sessionStorage.setItem('SXMonitor', JSON.stringify(data))
        this.setUserData(data.user)
        this.setZoneParams(data.zoneParams)
      } else {
        const reskeyList = [
          'org_level',
          'org_code',
          'dept_name',
          'org_code',
          'dept_name',
          'province_area_code',
          'province_area_name'
        ]
        // 新增的字段,在层级选择框有用
        const currKeyList = [
          'zone_level',
          'big_area_code',
          'big_area_name',
          'dept_code',
          'dept_name',
          'data_permission_code',
          'data_permission_name'
        ]
        // 层级转换 (只开放到33) 20250123日修改：新增 34--加盟区层级

        const levelList = [30, 32, 33, 34]
        const authList = ysAuthRes.filter(item => levelList.includes(+item['org_level']))

        // if(ysAuthRes[0][''])
        const org_level = ysAuthRes[0]['org_level']
        if (!authList.length) {
          this.isTrue = false
          this.$router.push({ name: 'noRights' })
          return
        }
        const levelCodeObj = {
          30: '0',
          31: '1', // 战区(已取消)
          32: '2',
          33: '3'
        }
        authList.forEach(authItem => {
          // 以下代码层级选择用的(暂时保留)
          currKeyList.forEach((item, index) => {
            if (!index) {
              authItem['zone_level'] = levelCodeObj[+org_level] || '4' // 初始zone_level 赋值
            } else {
              authItem[item] = authItem[reskeyList[index]]
            }
          })
        })

        const deptInfo = orgArray.find(item => item.dept_code === ysAuthRes[0].org_code) || {}

        // 默认取第一个做初始权限
        const data = {
          user: {
            zoneLevel: ysAuthRes[0].zone_level,
            zoneCode: ysAuthRes[0].org_code,
            zoneName: ysAuthRes[0].dept_name
          },
          zoneParams: {
            zoneLevel: ysAuthRes[0].zone_level,
            zoneCode: ysAuthRes[0].org_code,
            zoneName: ysAuthRes[0].dept_name,
            province_area_code: ysAuthRes[0].province_area_code,
            province_area_name: ysAuthRes[0].province_area_name,
            parent_code: ysAuthRes[0].parent_code,
            parent_name: ysAuthRes[0].parent_name,
            // 注意:ysAuthRes的area_code可能是多个业务区code拼接,为了兼容旧代码,这里的area_code在 场站/网管层级作为父层级使用
            // parent_code后续代替area_code
            // area_code: ysAuthRes[0].area_code ? ysAuthRes[0].area_code.split(',')[0] : ysAuthRes[0].area_code,
            // area_name: ysAuthRes[0].area_name ? ysAuthRes[0].area_name.split(',')[0] : ysAuthRes[0].area_name
            area_code: ysAuthRes[0].area_code ? ysAuthRes[0].area_code.split(',')[0] : deptInfo.area_code,
            area_name: ysAuthRes[0].area_name ? ysAuthRes[0].area_name.split(',')[0] : deptInfo.area_name
          }
        }

        this.setSXMonitor(data)
        sessionStorage.setItem('SXMonitor', JSON.stringify(data))

        this.setAuthList(ysAuthRes)
        this.setUserData(data.user)
        this.setZoneParams(data.zoneParams)
        // this.$router.replace({ path: '/sxMonitor' })
      }
      // if (ysAuthRes.length) {
      //   const reskeyList = [
      //     'data_permission_code',
      //     'data_permission_name',
      //     'dept_code',
      //     'dept_name',
      //     'managed_dept_level'
      //   ]
      //   // 新增的字段,在层级选择框有用
      //   const currKeyList = ['big_area_code', 'big_area_name', 'ky_hq_code', 'ky_hq_name', 'zone_level']
      //   // 层级转换 (只开放到33)
      //   const levelCodeObj = {
      //     30: 0,
      //     31: 1, // 战区(已取消)
      //     32: 2,
      //     33: 3
      //   }
      //   console.log(ysAuthRes, 'ysAuthRes')
      //   // if(ysAuthRes[0][''])
      //   const managed_dept_level = ysAuthRes[0]['managed_dept_level']
      //   if (!levelCodeObj[+managed_dept_level]) {
      //     this.isTrue = false
      //     this.$router.push({ name: 'noRights' })
      //     return
      //   }
      //   // 以下代码层级选择用的(暂时保留)
      //   currKeyList.forEach((item, index) => {
      //     if (index === 4) {
      //       // ysAuthRes[0][item] = levelCodeObj[+ysAuthRes[0][reskeyList[index]]] // 初始zone_level 赋值
      //       ysAuthRes[0]['zone_level'] = levelCodeObj[+ysAuthRes[0]['managed_dept_level']] || 4 // 初始zone_level 赋值
      //     } else {
      //       ysAuthRes[0][item] = ysAuthRes[0][reskeyList[index]]
      //     }
      //   })
      //   console.log(ysAuthRes, 'ysAuthRes---')
      //   // 默认取第一个做初始权限
      //   const data = {
      //     user: {
      //       zoneLevel: levelCodeObj[ysAuthRes[0].managed_dept_level],
      //       zoneCode: ysAuthRes[0].data_permission_code,
      //       zoneName: ysAuthRes[0].data_permission_name
      //     },
      //     zoneParams: {
      //       zoneLevel: levelCodeObj[ysAuthRes[0].managed_dept_level],
      //       zoneCode: ysAuthRes[0].data_permission_code,
      //       zoneName: ysAuthRes[0].data_permission_name
      //     }
      //   }

      //   this.setSXMonitor(data)
      //   sessionStorage.setItem('SXMonitor', JSON.stringify(data))

      //   this.setAuthList(ysAuthRes)
      //   this.setUserData(data.user)
      //   this.setZoneParams(data.zoneParams)
      //   // this.$router.replace({ path: '/sxMonitor' })
      // }
      this.$emit('onComplete', true)
      if (process.env.NODE_ENV === 'development') {
        this.isZoomOut = true
        this.isShow = false
      } else {
        setTimeout(() => {
          this.isZoomOut = true
        }, 500)
        setTimeout(() => {
          this.isShow = false
        }, 1000)
      }
    },
    // 用户主题模块权限
    _getThemeDataAuth() {
      return this.sendJavaRequest(
        {
          url: '/resourceServices/themePermission/queryUserThemeDetails',
          data: {
            userNo: this.userNo
          }
        },
        false,
        false
      )
    },
    // 请求主题模块地址(记录排序的顺序)
    _getModulesData() {
      const tableName = 't_sx_logbuch_menu_search'
      const conditionList = [{ key: 'userNo', value: this.userNo }]
      return this.sendOneDimenRequest(tableName, conditionList, false, false)
    },
    // 用户基础信息
    _getUserDeptCode() {
      return this.sendJavaRequest(
        {
          url: `/resourceServices/user/getDeptCode?userNo=${this.userNo}`,
          // url: `/resourceServices/user/getDeptCode?userNo=sx20040005`,
          // url: `/resourceServices/user/getDeptCode?userNo=sx21070360`,
          // url: `/resourceServices/user/getDeptCode?userNo=sx22120321`,
          method: 'GET'
        },
        false,
        false
      )
    },
    // 顺心的组织架构树
    _getOrgArray() {
      const tableName = 'sx_dim_department_2022'
      const conditionList = []
      return this.sendTwoDimenRequest(tableName, conditionList, false, false)
    },
    // 拿到映射数据
    _getYsAuthData(code) {
      const tableName = 'dim_sx_dept_permission_rel_dtl_df'
      const conditionList = [{ key: 'dept_code', value: code }]
      // const conditionList = [{ key: 'dept_code', value: 'SCN0434' }]
      return this.sendTwoDimenRequest(tableName, conditionList, false, false)
    },
    // 顺心战况人员额外权限接口
    _getYsAuthMoreData(useId) {
      const tableName = 'dm_sx_sxzk_data_permission_dtl_di'
      const conditionList = [{ key: 'emp_code', value: useId }]
      // const conditionList = [{ key: 'emp_code', value: 'sx21080064' }]
      // const conditionList = [{ key: 'emp_code', value: 'sx19080073' }]
      // const conditionList = [{ key: 'emp_code', value: 'sx19030003' }]
      // const conditionList = [{ key: 'emp_code', value: '20100460' }]
      // const conditionList = [{ key: 'emp_code', value: 'sx22120321' }]
      return this.sendTwoDimenRequest(tableName, conditionList, false, false)
    }
  }
}
</script>

<style lang="less" scoped>
.platform-container {
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background-color: #fff;
  z-index: 99999999;

  &.zoom-out {
    animation: fadeOut;
    /* referring directly to the animation's @keyframe declaration */
    animation-duration: 1s;
    /* don't forget to set a duration! */
    // display: none;
  }

  &.show-platform {
    display: none;
  }

  .lash-img {
    width: 100vw;

    &.height-100 {
      height: 100vh;
    }
  }

  .content-wrap {
    background-color: #f7f8fd;
    position: absolute;
    bottom: 0.8rem;
    left: 50%;
    transform: translateX(-50%);
    height: 0.44rem;
    width: 2.8rem;
    border: 1px solid rgba(151, 151, 151, 0.5);
    border-radius: 0.04rem;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0.2rem;

    .text {
      font-family: PingFangSC-Regular;
      font-size: 0.2rem;
      color: #333333;
      font-weight: 400;
      line-height: 0.44rem;
    }

    .text2 {
      font-family: PingFangSC-Medium;
      font-size: 0.24rem;
      color: #dc1e32;
      font-weight: 700;
      line-height: 0.44rem;
    }

    /deep/.num-card {
      display: flex;
      font-family: Roboto-Medium;
      font-size: 0.24rem;
      line-height: 0.44rem;
      color: #dc1e32;
      font-weight: 700;
    }
  }
}
</style>
