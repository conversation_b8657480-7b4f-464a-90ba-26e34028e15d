<template>
  <div>
    <CardList title="万件损坏率">
      <Tabs slot="nav" :options="tabData" :tabIndex="dateTypeIndex" @tabSelect="dateTypeSelect"></Tabs>
      <div class="pd_lr20">
        <BtnTabs class="mt32" :options="dataTypeList" :activeIndex="dataTypeIndex" @btnConfirm="btnConfirm" column="3">
        </BtnTabs>
        <div class="chart-container">
          <div class="right-btn-wrap" @click="handleShowProvince">
            展开{{ nextLevelName }} <i class="iconfont icon-dayuhao fs20"></i>
          </div>
          <KydChartModel class="mt32 pd_lr20" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
            <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
          </KydChartModel>
        </div>
      </div>
    </CardList>
    <ProvinceDrawer :show="provinceShow" :handleClose="handleProvinceClose" :title="provinceTitle"
      :tabs="WjhslProvinceTab" :request="getProvinceData"></ProvinceDrawer>
  </div>
</template>
<script>
import mixins from './commonMixins/mixins'
import { drawScrollChartNew } from 'common/charts/chartOption'
import request from './commonMixins/request'
import { DateTabLabels, WjhslProvinceTab } from '../constant'
import ProvinceDrawer from './provinceDrawer.vue'
import moment from 'moment'
import { mapMutations, mapState } from 'vuex'
import { DateTabTypes } from '../constant'

const typeData = [
  {
    label: '整体损坏率',
    keys: ['damageQtyRate', 'totalTargetPiece']
  },
  {
    label: '网络万件损坏率',
    keys: ['wlDamageQtyRate']
  },
  {
    label: '运营万件损坏率',
    keys: ['yyDamageQtyRate']
  },
  {
    label: 'SX场站万件损坏率',
    keys: ['sxDamageQtyRate']
  },
  {
    label: 'SF场站万件损坏率',
    keys: ['sfDamageQtyRate']
  }
]
const dataTypeList = typeData.map(item => item.label)
export default {
  mixins: [mixins, request],
  components: { ProvinceDrawer },
  data() {
    return {
      WjhslProvinceTab,
      provinceShow: false,
      tabData: [...DateTabLabels],
      dateTypeIndex: 0,
      dataTypeList: [...dataTypeList],
      dataTypeIndex: 0,
      dataSource: {},
      tableData: {},
      legendDataSource: [],
      provinceTitle: '万件损坏率情况'
    }
  },
  computed: {
    ...mapState('qualityBord', {
      qualityDate: state => state.all.dateDay,
      wjshlDay: state => state.quality.wjshlDay,
      wjshlWeek: state => state.quality.wjshlWeek,
      wjshlMon: state => state.quality.wjshlMon
    }),
    nextLevelName() {
      if (+this.zoneLevel === 34) {
        return '网点'
      }
      return this.zoneData.dLevelName === '网管' ? '网点' : this.zoneData.dLevelName
    },
    legendOption() {
      return {
        type: 'line',
        colNum: 3,
        isGrid: true,
        options: typeData[this.dataTypeIndex].keys.map((item, i) => {
          return {
            label: ['实际达成值', '目标值'][i],
            int: [2],
            seriesIndex: i
          }
        }),
        dataSource: this.legendDataSource
      }
    }
  },
  watch: {
    qualityDate(val) {
      this.init()
    },
    zoneCode(val) {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    ...mapMutations('qualityBord', ['setPageData']),
    async getProvinceData() {
      const time = moment(this.qualityDate).format('YYYY-MM-DD')
      const params = {
        dateType: DateTabTypes[this.dateTypeIndex]?.value,
        incDate: time,
        zoneLevel: this.zoneLevel
      }
      if (this.level_code) {
        params[this.level_code] = this.zoneCode
      }
      const data = await this._getWjshlDataDetail(params)
      return data?.obj || []
    },
    handleShowProvince() {
      this.provinceShow = true
    },
    handleProvinceClose() {
      this.provinceShow = false
    },
    init() {
      this.getData().then(() => {
        this.initData()
      })
    },
    async getData() {
      const time = moment(this.qualityDate).format('YYYY-MM-DD')
      const params = {
        dateType: DateTabTypes[this.dateTypeIndex]?.value,
        incDate: time,
        zoneLevel: this.zoneLevel
      }
      if (this.level_code) {
        params[this.level_code] = this.zoneCode
      }
      const data = await this._getWjshlData(params)
      const obj = data.obj
      this.setPageData({
        type: 'quality',
        dataType: 'wjshlDay',
        data: obj?.day
      })
      this.setPageData({
        type: 'quality',
        dataType: 'wjshlWeek',
        data: obj?.week
      })
      this.setPageData({
        type: 'quality',
        dataType: 'wjshlMon',
        data: obj?.month
      })
    },
    dateTypeSelect(index) {
      this.dateTypeIndex = index
      this.initData()
    },
    btnConfirm({ index }) {
      this.dataTypeIndex = index
      this.initData()
    },
    initData() {
      const dataList = [this.wjshlDay, this.wjshlWeek, this.wjshlMon]
      const currentData = dataList[this.dateTypeIndex]
      const dataTypeIndex = this.dataTypeIndex
      const { keys } = typeData.find((item, index) => index === dataTypeIndex)
      this.initModelChart(currentData, keys)
    },
    initModelChart(result, keys) {
      const fliterList = JSON.parse(JSON.stringify(result))
      const xData = []
      const sData = [[], []]
      if (fliterList && fliterList.length > 0) {
        fliterList.forEach((item, index) => {
          xData.push(item[this.key_dateunit])
          keys.forEach((key, i) => {
            sData[i].push({
              value: item[key] || 0,
              ...item
            })
          })
        })
      }
      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        grid: {
          top: 12
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              interval: 0, // 每个标签都显示
              formatter: value => {
                if (this.dateTypeIndex === 0) {
                  return this.$dateFormat(value, this.dateIndex)
                } else if (this.dateTypeIndex === 1) {
                  return `第${value}周`
                } else {
                  return `${moment(value).format('M')}月`
                }
              }
            }
          }
        ],
        yAxis: [
          {
            axisLabel: {
              formatter: (value, ...reset) => {
                return value
                // return `${numToPercent(value, 1)}`
              }
            }
          }
        ],
        series: keys.map((s, i) => {
          return {
            type: 'line',
            data: sData[i],
            smooth: true
          }
        })
      }
      drawScrollChartNew(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.multi_data_list_wrapper {
  background-color: #fff !important;
}

/deep/ .chart_legend_box {
  width: 85% !important;
}

.gauge-chart-box {
  height: 1.76rem;
  width: 96%;
}

.chart-container {
  position: relative;
  padding-top: 0.12rem;
}

.right-btn-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.12rem 0.16rem;
  border-radius: 0.4rem;
  background: #f8f9fc;
  font-size: 0.24rem;
  position: absolute;
  right: 0;
  top: 0.30rem;
}

.data_list_custom {
  background-color: #fff !important;
}

.text_conent {
  height: 0.81rem;
  width: 3.34rem;
  background: #f8f9fc;
  border-radius: 8px;
}
</style>
