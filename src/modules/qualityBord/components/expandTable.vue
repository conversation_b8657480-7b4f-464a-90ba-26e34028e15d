<!--
 * @Author: ZHANG
 * @Date: 2021-06-25 13:53:55
 * @LastEditTime: 2023-12-12 10:01:04
 * @Description: 可展开嵌套的表格
-->
<template>
  <div>
    <div class="list_item">
      <div class="item_parent_row table_header">
        <!-- <div class="col_item flex_center">排名</div>
        <div class="col_item flex_center">省区</div>
        <div class="col_item flex_center">指标操作</div> -->
        <div class="col_item flex_center" v-for="(parentItem, parentIndex) in parentColumns" :key="parentIndex">{{ parentItem.label }}</div>
      </div>
      <div class="list_item_warp">
        <div v-for="(item, index) in diaTableDataSource" :key="index">
          <div :class="index%2 === 1 ? 'item_parent_row row_bg' : 'item_parent_row'">
            <div class="col_item flex_center">
              <div class="rank_top" :style="`backgroundImage: url(${getRankIcon(item.allRank)}`" v-if="item.allRank <= 3"></div>
              <div class="rank_top" :style="`backgroundImage: url(${getRankIcon(item.allRank)}`" v-else-if="(item.allRank === 4 || item.allRank === 5) && levelName === '区域'">
                <div class="rank_top_text">第{{item.allRank}}名</div>
              </div>
              <div class="rank_tail" v-else>{{item.allRank}}</div>
            </div>
            <div class="col_item flex_center">{{ item.orgName }}</div>
            <div class="col_item flex_center expand_text" @click="handleExpand(item, index)">{{ item.isExpand ? '收起' : '展开' }}
              <i :class="[
                'iconfont',
                'icon-kyd-dayuhao',
                'fs20',
                'ml8',
                'fw500',
                item.isExpand ? 'expand-open' : 'expand-close'
              ]"></i>
            </div>
          </div>
          <div class="item_table_row" v-if="item.isExpand">
            <NormalTable :columns="childColumns" :dataSource="item.children || []"/>
          </div>
        </div>
      </div>
      <div class="empty_zk_img" v-if="!diaTableDataSource || diaTableDataSource.length<1">
        <img :src="empty_zk1" alt="" srcset="">
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import baseMixins from '../baseMixins'
import medalOneImg from '../img/medal_one.png'
import medalTwoImg from '../img/medal_two.png'
import medalThreeImg from '../img/medal_three.png'
import medalNormalImg from '../img/medal_normal.png'
import empty_zk1 from '../img/empty_zk1.png'
import { numToPercent, numToInteger } from 'common/js/numFormat'
export default {
  name: 'expandTable',
  mixins: [baseMixins],
  components: {},
  props: {
    // 父表格的columns
    parentColumns: {
      type: Array,
      default() {
        return []
      }
    },
    // 子表格的columns
    childColumns: {
      type: Array,
      default() {
        return []
      }
    },
    // 表格数据
    datas: {
      type: Array,
      default() {
        return []
      }
    },
    levelName: {
      type: String,
      default() {
        return '省区'
      }
    }
  },
  data() {
    return {
      empty_zk1,
      diaTableDataSource: [],
      // columns示例
      diaTableColumns: [
        {
          label: '指标类型',
          dataIndex: 'targetName'
        },
        {
          label: '排名',
          dataIndex: 'targetRank',
          align: 'center'
        },
        {
          label: '达成值',
          dataIndex: 'targetValue',
          align: 'center',
          render(_, i) {
            return numToInteger(i, 2)
          }
        },
        {
          label: '完成比',
          dataIndex: 'targetCom',
          align: 'center',
          render(_, i) {
            return numToPercent(i, 2)
          }
        }
      ]
    }
  },
  computed: {
    ...mapState({
      holidayData: 'holidayData'
    })
  },
  watch: {
    datas(val) {
      this.diaTableDataSource = val || []
    }
  },
  methods: {
    closeTopModal() {
      this.tipVisible = false
    },
    // 展开
    handleExpand(row, index) {
      this.diaTableDataSource[index].isExpand = !row.isExpand
    },
    // 格式化数字
    numFormat(value, colItem) {
      if (colItem?.per) {
        const str = numToPercent(value, ...(colItem?.per || [])) + ''
        return str.replace(/%/, '')
      } else if (colItem?.int) {
        return numToInteger(value, ...(colItem?.int || []))
      } else {
        return value
      }
    },
    // 排名icon
    getRankIcon(value) {
      const iconObj = {
        1: medalOneImg,
        2: medalTwoImg,
        3: medalThreeImg,
        4: medalNormalImg,
        5: medalNormalImg
      }
      return iconObj[value] || ''
    }
  },
  mounted() {
  }
}
</script>
<style lang="less" scoped>
/deep/.table-outer-wrap {
  background: #F1F3FB !important;
}
.list_item {
  box-sizing: border-box;
  font-family: PingFangSC;
  font-size: 13px;
  color: #333333;
}
.item_parent_row{
  display: grid;
  grid-template-columns: 1fr 1.5fr 1fr;
  height: 40px;
  border-radius: 4px;
  line-height: 40px;
  background: #ffffff;
  box-shadow: inset 0px -1px 0px 0px #DDE3EE;
}
.row_bg {
  background: #FAFAFA;
  box-shadow: inset 0px -1px 0px 0px #DDE3EE;
}
.table_header {
  background: #F8FAFF;
  font-weight: 700;
  font-size: 13px;
  box-shadow: none;
}
.expand_text {
  color: #CC1B23;
}
.list_item .item_table_row {
  /deep/.sbody tr td {
    background: #F1F3FB !important;
    border-top: 1px solid rgba(221, 227, 238, 0.6);
  }
  /deep/.kyd-table-outer-wrap-container .table-outer-show .table-outer-wrap .table-wrap.small .sbody tr.row_bgc>td {
    background: #F1F3FB !important;
  }
}
.expand-open {
  transform: rotate(-90deg);
  transition: 200ms linear;
}
.expand-close {
  transform: rotate(90deg);
  transition: 200ms linear;
}
.empty_zk_img {
  height: 120px;
  width: 120px;
  margin: auto;
  img {
    width: 100%;
    height: 100%;
  }
}
.rank_top {
  height: 36px;
  width: 36px;
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
}
.rank_top_text {
  font-family: DIN-BlackItalic;
  font-size: 8px;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
  letter-spacing: 0.01em;
  font-variation-settings: "opsz" auto;
  background: linear-gradient(295deg, #525252 7%, #9B9B9B 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.rank_tail {
  height: 17px;
  width: 17px;
  font-size: 12px;
  line-height: 17px;
  border-radius: 50%;
  border: 1px solid rgba(221, 221, 221, 0.4724);
  background: rgba(221, 221, 221, 0.4724);
  color: #AFB7C0;
  text-align: center;
}

</style>
