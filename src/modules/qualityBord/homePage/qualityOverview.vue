<!--
 * @Author: ZHANG
 * @Date: 2021-06-25 13:53:55
 * @LastEditTime: 2023-12-12 10:01:04
 * @Description:
-->
<template>
  <div>
    <CardList title="品质类指标总览">
      <OverviewList :options="qualityOverviewList" :data="overviewData"></OverviewList>
      <!-- <div class="quality_card">
        <div class="card_list_item">
          <div class="list_item_top">百万件遗失率</div>
          <div class="list_item_middle">
            <div class="list_item_middle_value">32</div>
            <div class="list_item_middle_unit">%</div>
          </div>
          <div class="list_item_bottom">
            <div class="list_item_bottom_item">
              <span class="bottom_item_label">月环比</span>
              <span class="bottom_item_value">32.6%</span>
            </div>
            <div class="list_item_bottom_item">
              <span class="bottom_item_label">完成比</span>
              <span class="bottom_item_value">189.2%</span>
            </div>
          </div>
          <div class="medal_box" :style="`backgroundImage: url(${medalList[0]})`"></div>
        </div>

        <div class="card_list_item">
          <div class="list_item_top">百万件遗失率</div>
          <div class="list_item_middle">
            <div class="list_item_middle_value">32</div>
            <div class="list_item_middle_unit">%</div>
          </div>
          <div class="list_item_bottom">
            <div class="list_item_bottom_item">
              <span class="bottom_item_label">月环比</span>
              <span class="bottom_item_value">32.6%</span>
            </div>
            <div class="list_item_bottom_item">
              <span class="bottom_item_label">完成比</span>
              <span class="bottom_item_value">189.2%</span>
            </div>
          </div>
          <div class="medal_box" :style="`backgroundImage: url(${medalList[1]})`"></div>
        </div>

        <div class="card_list_item">
          <div class="list_item_top">百万件遗失率</div>
          <div class="list_item_middle">
            <div class="list_item_middle_value">32</div>
            <div class="list_item_middle_unit">%</div>
          </div>
          <div class="list_item_bottom">
            <div class="list_item_bottom_item">
              <span class="bottom_item_label">月环比</span>
              <span class="bottom_item_value">32.6%</span>
            </div>
            <div class="list_item_bottom_item">
              <span class="bottom_item_label">完成比</span>
              <span class="bottom_item_value">189.2%</span>
            </div>
          </div>
          <div class="medal_box" :style="`backgroundImage: url(${medalList[2]})`"></div>
        </div>

        <div class="card_list_item">
          <div class="list_item_top">百万件遗失率11</div>
          <div class="list_item_middle">
            <div class="list_item_middle_value">32</div>
            <div class="list_item_middle_unit">%</div>
          </div>
          <div class="list_item_bottom">
            <div class="list_item_bottom_item">
              <span class="bottom_item_label">月环比</span>
              <span class="bottom_item_value">32.6%</span>
            </div>
            <div class="list_item_bottom_item">
              <span class="bottom_item_label">完成比</span>
              <span class="bottom_item_value">189.2%</span>
            </div>
          </div>
          <div class="medal_box" :style="`backgroundImage: url(${medalList[3]})`">
            <div class="medal_box_title">第<span>9</span>名</div>
          </div>
        </div>
      </div> -->
    </CardList>
  </div>
</template>
<script>
// import { mapState } from 'vuex'
import OverviewList from '../components/overviewList.vue'
import { qualityOverviewList } from '../constant'
import mixins from './commonMixins/mixins'
import medal_one from '../img/medal_one.png'
import medal_two from '../img/medal_two.png'
import medal_three from '../img/medal_three.png'
import medal_normal from '../img/medal_normal.png'
export default {
  name: 'QualityOverview',
  mixins: [mixins],
  components: { OverviewList },
  data() {
    return {
      medalList: [medal_one, medal_two, medal_three, medal_normal],
      medal_three,
      tipVisible: false,
      tipConfig: {
        title: '提示',
        content: '123'
      },
      qualityOverviewList
    }
  },
  computed: {},
  watch: {
    overviewData(val) {
    }
  },
  methods: {
    dateChange() {},
    closeTopModal() {
      this.tipVisible = false
    }
  },
  mounted() {
  }
}
</script>
<style lang="less" scoped>
.profit_page {
  color: #333;
}

.date_container {
  width: 100vw;
  height: 0.8rem;
  padding: 0 0.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quality_card {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.2rem;
  padding: 0 0.2rem;
  margin-top: 0.2rem;
}
.card_list_item {
  background: #F6F6F6;
  color: #333;
  border-radius: 0.08rem;
  padding: 0.2rem;
  box-sizing: border-box;
  position: relative;
  .list_item_top {
    font-size: 0.24rem;
    margin-bottom: 0.16rem;
  }
  .list_item_middle {
    font-size: 0.48rem;
    margin-bottom: 0.16rem;
    display: flex;
    justify-content: flex-start;
    align-items: baseline;
    .list_item_middle_value {
      font-weight: bold;
    }
    .list_item_middle_unit {
      font-size: 0.32rem;
      margin-left: 0.04rem;
    }
  }
  .list_item_bottom {
    font-size: 0.20rem;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.24rem;
  }
  .list_item_bottom_item {
    .bottom_item_label {
      color: #999999;
    }
    .bottom_item_value{
      color: #333333;
      margin-left: 0.08rem;
      font-weight: bold;
    }
  }
  .medal_box {
    // background: url(../img/medal_one.png) no-repeat;
    background-repeat: no-repeat;
    background-size: 100%;
    height: 1.2rem;
    width: 1.2rem;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .medal_box_title {
      background: linear-gradient(293deg, #525252 7%, #9B9B9B 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-size: 0.22rem;
      font-weight: 500;
      span {
        font-family: DIN-BlackItalic;
        font-size: 0.24rem;
      }
    }
  }
}

</style>
