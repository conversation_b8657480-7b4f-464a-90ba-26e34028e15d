<!--
 * @Author: shigl
 * @Date: 2024-09-19 18:17:37
 * @LastEditTime: 2024-09-19 18:17:37
 * @Description: 运营--品效
-->
<template>
  <div>
    <div class="date_container">
      <KyDatePicker :type="dateType" :dateValue="dateValue" @onChange="dateChange" :holidayData="holidayData">
      </KyDatePicker>
      <Tabs :options="dateTypeOption" :tabIndex="tabIndex" @tabSelect="tabSelect" />
    </div>
    <PageContent>
      <OverView :areaCode="apiParams.areaCode" :dataLevel="apiParams.dataLevel"
        :provinceAreaCode="apiParams.provinceAreaCode" :currentDateType="currentDateType"></OverView>
      <PlanTimeReach :areaCode="apiParams.areaCode" :dataLevel="apiParams.dataLevel"
        :provinceAreaCode="apiParams.provinceAreaCode" :currentDateType="currentDateType"
        @dateTypeChange="handleDateTypeChange"></PlanTimeReach>
      <gxfyChart :areaCode="apiParams.areaCode" :dataLevel="apiParams.dataLevel"
        :provinceAreaCode="apiParams.provinceAreaCode" :currentDateType="currentDateType"
        @dateTypeChange="handleDateTypeChange"></gxfyChart>
      <zzlxChart :areaCode="apiParams.areaCode" :dataLevel="apiParams.dataLevel"
        :provinceAreaCode="apiParams.provinceAreaCode" :currentDateType="currentDateType"
        @dateTypeChange="handleDateTypeChange"></zzlxChart>
      <mdspChart :areaCode="apiParams.areaCode" :dataLevel="apiParams.dataLevel"
        :provinceAreaCode="apiParams.provinceAreaCode" :currentDateType="currentDateType"
        @dateTypeChange="handleDateTypeChange"></mdspChart>
      <div style="height: 0.5rem"></div>
      <KyDataDrawer :visible="visible" :title="diaTitle" height="80%" @close="diaClose">
      </KyDataDrawer>
    </PageContent>
  </div>
</template>
<script>
import request from './request'
import mixins from './commonMixins/mixins'
import flowLayout from 'common/mixins/flowLayout.js'
import OverView from './overview'
import PlanTimeReach from './planTimeReach'
import zzlxChart from './components/zzlx/zzlxChart.vue'
import gxfyChart from './components/gxfy/gxfyChart.vue'
import mdspChart from './components/mdsp/mdspChart.vue'
import { mapMutations, mapState, mapGetters } from 'vuex'
export default {
  mixins: [request, flowLayout, mixins],
  components: {
    OverView,
    PlanTimeReach,
    zzlxChart,
    gxfyChart,
    mdspChart,
  },
  data() {
    return {
      diaTitle: '标题',
      visible: false,
      apiParams: {
        areaCode: '',
        dataLevel: 30,
        provinceAreaCode: ''
      },
      // 日期类型切换相关数据
      dateTypeOption: ['日', '周', '月'],
      tabIndex: 0
    }
  },
  computed: {
    dateType() {
      // 固定返回 'day'，确保 KyDatePicker 组件始终显示日期选择
      return 'day'
    },
    // 新增：当前选中的日期类型，用于传递给子组件或API调用
    currentDateType() {
      // 1=日，2=周，3=月
      return this.tabIndex + 1
    },
    // 新增：当前日期类型的字符串表示，用于API调用
    currentDateTypeString() {
      return ['day', 'week', 'month'][this.tabIndex]
    },
    ...mapState({
      holidayData: 'holidayData'
    }),

    // 从 zoneInfo store 中获取区域信息
    ...mapGetters('zoneInfo', [
      'currentZoneInfo',           // 当前区域基本信息
      'parentProvinceAreaCode',    // 上级省区code
      'parentProvinceAreaName',    // 上级省区名称
      'parentAreaCode',            // 上级区域code
      'parentAreaName',            // 上级区域名称
      'parentZoneInfo',            // 完整的上级信息对象
      'breadcrumbData',            // 面包屑导航数据
      'isStationLevel'             // 是否为场站级别
    ])
  },
  watch: {
    zoneCode() {
      this.initOptions()
      this.buildApiParams()
    },
    dateDay() {
      this.buildApiParams()
    },
    // 监听 zoneInfo store 的变化
    parentZoneInfo: {
      handler(newVal, oldVal) {
        console.log('=== index.vue: zoneInfo 数据变化 ===')
        console.log('新的上级信息:', newVal)
        console.log('旧的上级信息:', oldVal)

        // 当区域信息变化时，重新构建API参数
        if (newVal && newVal.currentCode !== (oldVal && oldVal.currentCode)) {
          console.log('区域发生变化，重新构建API参数')
          this.buildApiParams()
        }
      },
      deep: true,
      immediate: false
    }
  },
  methods: {
    ...mapMutations('operationNew', ['setDate', 'setDateIndex', 'setPageData']),
    dateChange(date) {
      this.setDate({
        type: 'all',
        key: 'dateDay',
        // key: ['dateDay', 'dateMon'][this.dateIndex],
        date: this.$moment(date).format(['YYYYMMDD', 'YYYYMM'][this.dateIndex])
      })
      this.initOptions()
    },
    initOptions() { },
    diaClose() {
      this.visible = false
    },
    btnOpenDia(data) {
      this.title = data
      this.visible = true
    },

    // 日期类型切换方法（日/周/月切换）
    tabSelect(index) {
      this.tabIndex = index
      // 注意：这里只更新tabIndex，不会影响KyDatePicker的dateType（固定为'day'）
      // 子组件通过currentDateType prop接收新的日期类型参数
      this.initOptions()
    },

    // 处理子组件的日期类型变化事件
    handleDateTypeChange(dateType) {
      // 更新tabIndex，这会触发currentDateType计算属性的变化
      this.tabIndex = dateType - 1
      this.initOptions()
    },

    // 构建API请求参数
    buildApiParams() {
      // 数据层级 30顺心 32省区 33区域 99网点
      const dataLevelMap = {
        provinceAreaCode: 32,
        areaCode: 33,
        deptCode: 99,
        default: 30
      }

      const params = {
        // 数据层级 30顺心 32省区 33区域 - 根据当前选中层级判断
        dataLevel: dataLevelMap[this.key_level_code] || dataLevelMap.default,
        areaCode: '',
        provinceAreaCode: ''
      }

      // 根据当前级别设置 provinceAreaCode 和 areaCode
      if (dataLevelMap[this.key_level_code] === 33) {
        params.provinceAreaCode = this.parentProvinceAreaCode
        params.areaCode = this.zoneCode
      }
      if (dataLevelMap[this.key_level_code] === 32) {
        params.provinceAreaCode = this.zoneCode
      }

      this.apiParams = params
    }
  },
  mounted() {
    this.setDateIndex({
      type: 'all',
      index: 0
    })
    // 初始化API参数
    this.buildApiParams()
  }
}
</script>
<style lang='less' scoped>
.date_container {
  width: 100vw;
  height: 0.8rem;
  padding: 0 0.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;

  // 确保日期选择器在左边
  /deep/ .ky-date-picker {
    flex: 0 0 auto;
  }

  // 确保日期类型切换在右边
  /deep/ .tabs-container {
    flex: 0 0 auto;
    margin-left: auto;
  }
}
</style>
