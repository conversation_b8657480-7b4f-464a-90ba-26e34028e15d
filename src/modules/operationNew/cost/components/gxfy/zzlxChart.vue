<!--
 * @Author: gilshi
 * @Date: 2021-07-10 22:20:01
 * @LastEditTime: 2021-07-16 17:00:22
 * @Description:规划时效达成情况
-->
<template>
  <div>
    <CardList title="环节成本单价趋势（T-1）">
      <div class="pd_lr20">
        <BtnTabs class="mt32 ft28" :options="dataTypeList" :activeIndex="btnIndex" @btnConfirm="btnConfirm" column="3">
        </BtnTabs>
        <div class="chart_wrap_content">
          <div class="chart_btn_wrap" v-if="zoneLevel < 33">
            <div class="right_btn_wrap" @click="btnOpenDia">
              展开{{ level_next_name_zh }} <i class="iconfont icon-dayuhao fs20"></i>
            </div>
          </div>
          <KydChartModel class="mt32" :legendOption="legendOption" :tableOption="tableData" tableLength="5">
            <div class="mt24" ref="chart-model-trend" style="height: 3.2rem"></div>
          </KydChartModel>
        </div>
      </div>
    </CardList>
    <TabsDrawer :show="visible" :handleClose="handleClose" :title="drawerTitle" :tabs="tabOptions"
      :request="getPlanTimeResultDataTable"></TabsDrawer>
  </div>
</template>
<script>
import { overAllProfit, planTimeReachTab, costPerKgTab } from '../../commonMixins/config'
import { drawScrollChart } from 'common/charts/chartOption'
import TabsDrawer from './zzlxDrawer.vue'
import { mapState, mapGetters } from 'vuex'
import requestMixins from 'common/mixins/requestMixins'
import baseMixins from '../../../baseMixins'
const typeData = [
  {
    label: '营运单公斤',
    serise: [
      {
        label: '本月',
        render: (_, item, data) => {
          const value = data?.data?.yyCostPerKg || 0
          return parseFloat(value) || 0  // 直接返回数值，用于图表绘制
        }
      },
      {
        label: '上月同日',
        render: (_, item, data) => {
          // 修复：应该从 prevData 获取上月同日数据
          const value = data?.prevData?.yyCostPerKg || 0
          return parseFloat(value) || 0  // 直接返回数值，用于图表绘制
        }
      }
    ]
  },
  {
    label: '干线单公斤',
    serise: [
      {
        label: '本月',
        render: (_, item, data) => {
          // 您可以在这里选择任意字段，甚至进行复杂计算
          const value = data.data?.gxCostPerKg || 0
          return parseFloat(value) || 0
        }
      },
      {
        label: '上月同日',
        render: (_, item, data) => {
          // 从上月数据中获取
          const value = data.prevData?.gxCostPerKg || 0
          return parseFloat(value) || 0
        }
      }
    ]
  },
  {
    label: '中转单公斤',
    serise: [
      {
        label: '本月',
        render: (_, item, data) => {
          // 您可以在这里选择任意字段，甚至进行复杂计算
          const value = data.data?.transferCostPerKg || 0
          return parseFloat(value) || 0
        }
      },
      {
        label: '上月同日',
        render: (_, item, data) => {
          // 从上月数据中获取
          const value = data.prevData?.transferCostPerKg || 0
          return parseFloat(value) || 0
        }
      }
    ]
  },
  {
    label: '收派单公斤',
    serise: [
      {
        label: '本月',
        render: (_, item, data) => {
          // 您可以在这里选择任意字段，甚至进行复杂计算
          const value = data.data?.spCostChRatePrev || 0
          return parseFloat(value) || 0
        }
      },
      {
        label: '上月同日',
        render: (_, item, data) => {
          // 从上月数据中获取
          const value = data.prevData?.spCostChRatePrev || 0
          return parseFloat(value) || 0
        }
      }
    ]
  },
]
const dataTypeList = typeData.map(item => item.label)
export default {
  mixins: [requestMixins, baseMixins],
  components: { TabsDrawer },
  props: {
    // 从父组件传入的API参数
    areaCode: {
      type: String,
      default: ''
    },
    dataLevel: {
      type: Number,
      default: 30
    },
    provinceAreaCode: {
      type: String,
      default: ''
    },
    // 从父组件传入的当前日期类型（1=日，2=周，3=月）
    currentDateType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      // btnData: ['规划时效总体达成', '三日内占比动态', '次日达动态', '次日达经济圈动态'],
      dataTypeList: [...dataTypeList],
      tableWidth: '100%',
      tableData: {},
      // 日期类型 1日 2周 3月
      dateType: 1,
      btnIndex: 0,
      tabIndex: 0,
      legendDataSource: [],
      dateTypeOption: ['日', '周', '月'],
      dateKeyList: ['day', 'week', 'month'],
      visible: false,
      drawerTitle: '环节成本单价趋势（T-1）',
      planTimeReachTab, // https://blog.csdn.net/m0_52252555/article/details/140273000
      costPerKgTab,
      // 组件内部数据，替代从 mixins 获取的数据
      planTimeResultData: {},
      dateTypes: [
        {
          label: '日',
          value: 'day'
        },
        {
          label: '周',
          value: 'week'
        },
        {
          label: '月',
          value: 'month'
        }
      ],
      date_value_key: ['statisticalDate', 'weekWid', 'monthWid']
    }
  },
  computed: {
    ...mapGetters('operationNew', {
      dateValue: 'dateValue'
    }),
    ...mapState({
      isDev: state => state.isDev
    }),
    ...mapState('operationNew', {
      dateIndex: state => state.all.dateIndex,
      dateDay: state => state.all.dateDay
    }),
    columns() {
      return _.defaultsDeep([], overAllProfit[this.btnIndex][this.dateIndex])
    },
    tabOptions() {
      console.log('🌹costPerKgTab', this.level_next_name);

      return costPerKgTab('siteName', this.tabIndex)
      // return costPerKgTab(this.level_next_name, this.tabIndex)
    },
    legendOption() {
      const currentSerise = typeData[this.btnIndex]?.serise
      return {
        type: 'line',
        colNum: 3,
        isGrid: true,
        options: currentSerise.map((item, index) => {
          return {
            seriesIndex: index,
            ...item
          }
        }),
        dataSource: this.legendDataSource
      }
    },
    // 从原 mixins 中移过来的计算属性
    level_next_value() {
      return {
        30: 32,
        32: 33,
        33: 39,
        34: null
      }[+this.zoneLevel]
    },
    level_next_name() {
      return {
        30: 'provinceAreaName',
        32: 'areaName',
        33: 'deptName',
        34: null
      }[+this.zoneLevel]
    },
    level_next_name_zh() {
      return {
        30: '省区',
        32: '区域',
        33: '场站',
        34: null
      }[+this.zoneLevel]
    },
    key_level_code() {
      return {
        30: null,
        32: 'provinceAreaCode',
        33: 'areaCode',
        34: 'deptCode'
      }[+this.zoneLevel]
    },
    // 添加其他需要的计算属性
    incDate() {
      if (!this.dateIndex) {
        return this.$moment(this.dateValue).subtract(15, 'days').format('YYYYMMDD')
      }
      return this.$moment(this.dateValue).subtract(5, 'month').format('YYYYMM')
    },
    key_dateunit() {
      return ['statisticalDate', 'weekWid', 'monthWid'][this.dateIndex]
    }
  },
  watch: {
    // 监听父组件传入的日期类型变化
    currentDateType: {
      handler(newVal) {
        if (newVal !== this.dateType) {
          this.dateType = newVal
          this.tabIndex = newVal - 1 // currentDateType 从1开始，tabIndex 从0开始
          // 重新获取数据
          this.getPlanTimeResultData().then(() => {
            this.initData()
          })
        }
      },
      immediate: true
    },
    planTimeResultData: {
      handler(val) {
        this.initData(val)
      },
      deep: true
    },
    btnIndex() {
      this.initData()
    },
    tabIndex() {
      this.initData()
    },
    zoneCode() {
      this.init()
    },
    dateDay() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    // 添加API请求方法，替代从 request mixins 中获取的方法
    _getYycbChartData(data) {
      return this.sendJavaRequest({
        url: '/cockpit/sxzk_operating_cost/historicalList',
        data: data
      })
    },
    async init() {
      this.getPlanTimeResultData().then(() => {
        this.initData()
      })
    },
    async getPlanTimeResultData() {
      const data = {
        areaCode: this.areaCode,
        // 数据层级 30顺心 32省区 33区域 99网点
        dataLevel: this.dataLevel,
        // dataLevel: 32,
        date: this.$moment(this.dateDay).format('YYYY-MM-DD'),
        // 日期类型 1日 2周 3月
        dateType: this.dateType,
        provinceAreaCode: this.provinceAreaCode
      }
      const response = await this._getYycbChartData(data)


      // 处理新的数据格式，根据dateType保存到不同字段
      if (response && response.obj && response.obj.itemList) {
        // 根据dateType确定保存的字段名
        const dateTypeKey = this.dateKeyList[this.dateType - 1] // dateType从1开始，数组从0开始

        // 保持原有的数据结构，但只更新对应的字段
        if (!this.planTimeResultData) {
          this.planTimeResultData = {}
        }

        // 根据不同的dateType，将数据保存到对应的字段
        this.planTimeResultData[dateTypeKey] = response.obj.itemList


        // 触发响应式更新
        this.$set(this, 'planTimeResultData', { ...this.planTimeResultData })
      }
    },
    btnConfirm({ index }) {
      this.btnIndex = index
      this.initData()
    },
    tabSelect(index) {
      // 注意：这里只更新本地的tabIndex，实际的dateType由父组件的currentDateType控制
      // 如果父组件没有传入currentDateType或者需要本地控制，则保持原有逻辑
      if (this.currentDateType === undefined || this.currentDateType === null) {
        // 兼容模式：如果父组件没有传入currentDateType，则使用本地控制
        this.tabIndex = index
        this.dateType = index + 1 // 更新dateType: 1日 2周 3月
        // 切换日周月时重新调用接口
        this.getPlanTimeResultData().then(() => {
          this.initData()
        })
      } else {
        // 新模式：由父组件控制，这里只触发父组件的回调（如果需要的话）
        // 父组件会通过currentDateType prop的变化来驱动子组件更新
        this.$emit('dateTypeChange', index + 1)
      }
    },
    getYycbChartDetailData(data) {
      return this.sendJavaRequest({
        url: '/cockpit/sxzk_operating_cost/list',
        data: data
      })
    },
    async getPlanTimeResultDataTable() {
      // const data = {
      //   dateType: this.dateKeyList[this.dateType - 1], // 使用 dateType 保持一致性
      //   levelCode: this.level_next_value,
      //   [this.key_level_code]: this.zoneCode,
      //   statisticalDate: this.$moment(this.dateDay).format('YYYY-MM-DD')
      // }
      const listAreaMap = {
        30: 32,
        32: 33,
        33: 99,
        99: 99
      }
      // const dataType = this.dateKeyList[this.dateType - 1] // 使用 dateType 保持一致性
      const data = {
        areaCode: this.areaCode,
        // 数据层级 30顺心 32省区 33区域 99网点
        // dataLevel: this.dataLevel,
        dataLevel: listAreaMap[this.dataLevel],
        date: this.$moment(this.dateDay).format('YYYY-MM-DD'),
        // 日期类型 1日 2周 3月
        dateType: this.dateType,
        provinceAreaCode: this.provinceAreaCode
      }
      const res = await this.getYycbChartDetailData(data)

      return res.obj || []
    },
    btnOpenDia() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    initData() {
      // 使用 dateType 来保持一致性，因为数据是根据 dateType 保存的
      const dataType = this.dateKeyList[this.dateType - 1] // dateType从1开始，数组从0开始
      const result = this.planTimeResultData[dataType]

      const dataSerise = typeData[this.btnIndex].serise
      const xData = []
      const sData = [[], []]

      if (result && result.length > 0) {
        const fliterList = JSON.parse(JSON.stringify(result))

        fliterList.forEach((item, itemIndex) => {
          // 使用 date 字段作为 x 轴数据
          xData.push(item.date)


          dataSerise.forEach((seriesConfig, i) => {
            let value = 0


            // 如果有自定义的 render 函数，则使用 render 函数处理数据
            if (seriesConfig.render && typeof seriesConfig.render === 'function') {
              // 修复：传递正确的三个参数给 render 函数
              // 第一个参数：通常是 h (createElement)，这里传 null
              // 第二个参数：当前项数据
              // 第三个参数：完整的数据对象，包含 data 和 prevData
              value = seriesConfig.render(null, item, item)
            } else {
              // 根据 isPrevData 标记决定从哪里获取数据
              if (seriesConfig.isPrevData) {
                // 从 prevData 中获取数据
                value = item.prevData?.[seriesConfig.dataIndex] || 0
              } else {
                // 从 data 中获取数据
                value = item.data?.[seriesConfig.dataIndex] || 0
              }
            }

            sData[i].push({
              ...item,
              value: parseFloat(value) || 0
            })
          })
        })
      }

      // 计算所有数据的最小值和最大值，确保两个Y轴使用相同的刻度范围
      let allValues = []
      sData.forEach(seriesData => {
        seriesData.forEach(item => {
          allValues.push(item.value)
        })
      })


      let globalMin = 0
      let globalMax = 100

      if (allValues.length > 0) {
        const minValue = Math.min(...allValues)
        const maxValue = Math.max(...allValues)
        const range = maxValue - minValue
        const padding = range > 0 ? range * 0.1 : 10 // 10%的边距，如果range为0则使用固定值
        globalMin = minValue - padding
        globalMax = maxValue + padding
      }

      this.legendDataSource = []
      const option = {
        tooltip: {
          formatter: params => {
            this.legendDataSource = params
          }
        },
        grid: {
          top: 15
        },
        xAxis: [
          {
            data: xData,
            axisLabel: {
              interval: 0, // 每个标签都显示
              formatter: value => {
                if (this.tabIndex === 0) {
                  return this.$dateFormat(value, this.tabIndex)
                } else if (this.tabIndex === 1) {
                  return `第${value.slice(-2)}周`
                } else {
                  return `${this.$moment(value).format('MM')}月`
                }
              }
            }
          }
        ],
        yAxis: typeData[this.btnIndex].serise.map((_, index) => {
          const colorList = ['#4c83f9', '#ff8066']
          return {
            scale: false,
            position: index === 0 ? 'left' : 'right', // 第一个Y轴在左侧，第二个在右侧
            axisLine: {
              show: true, // 显示y轴线
              lineStyle: {
                color: colorList[index] // 设置Y轴的颜色
              }
            },
            splitLine: {
              show: index === 0, // 只在第一个Y轴显示分割线
              lineStyle: {
                type: 'dashed'
              }
            },
            axisLabel: {
              show: index === 0, // 只在第一个Y轴（左侧）显示标签
              formatter: (value) => {
                return Number(value).toFixed(2)
                // return this.$perFormat(value)
              }
            },
            min: globalMin, // 使用统一的最小值
            max: globalMax  // 使用统一的最大值
          }
        }),
        series: typeData[this.btnIndex]?.serise.map((seriesConfig, i) => {

          return {
            type: 'line',
            data: sData[i],
            smooth: false,
            yAxisIndex: i,
            name: seriesConfig.label // 添加系列名称，便于图例显示
          }
        })
      }
      drawScrollChart(option, this.$refs['chart-model-trend'])
      this.tableData = {
        options: option
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .chart_legend_box {
  width: 85% !important;
}

.chart_wrap_content {
  position: relative;
}

.chart_btn_wrap {
  position: absolute;
  right: 0.08rem;
  top: -0.10rem;
}

.right_btn_wrap {
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.12rem 0.16rem;
  border-radius: 0.4rem;
  background: #f8f9fc;
  font-size: 0.24rem;
  color: #333333;
}
</style>
