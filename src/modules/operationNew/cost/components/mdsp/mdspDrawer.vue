<template>
  <KyDataDrawer :visible="show" :title="title" height="80%" @close="diaClose" @drawerHeight="mixinsDrawerHeight">
    <div class="flex_between pd_lr20">
      <div class="tab-list">
        <div class="tab-list__item" v-for="(i, index) in tabs" :key="index"
          :class="{ 'active-tab': selectedTabIndex === index }" @click="selectTab(index)">
          {{ i?.label }}
        </div>
      </div>
    </div>
    <NormalTable :columns="tableColumns" :dataSource="sortData" :maxHeight="mixinsTableMaxHeight" />
  </KyDataDrawer>
</template>
<script>
import request from '../../commonMixins/request'
export default {
  name: 'ZzlxDrawer',
  mixins: [request],
  props: {
    show: {
      type: Boolean,
      default: false
    },
    handleClose: {
      type: Function,
      default() {
        return () => { }
      }
    },
    title: {
      type: String,
      default: ''
    },
    tabs: {
      type: Array,
      default() {
        return []
      }
    },
    columns: {
      type: Array,
      default() {
        return []
      }
    },
    request: {
      type: Function,
      default() {
        return () => { }
      }
    }
  },
  computed: {
    tableColumns() {
      return this.tabs[this.selectedTabIndex]?.keys.map(i => {
        return i
      })
    },
    sortData() {
      const sortKey = this.tabs[this.selectedTabIndex]?.sortKey
      const newKist = [...this.data]
      if (sortKey) {
        this.$objectSortDown(newKist, sortKey)
        return newKist
      } else {
        return this.data
      }
    }
  },
  data() {
    return {
      mixinsTableMaxHeight: '8rem',
      visible: false,
      selectedTabIndex: 0, // 选中的tab索引
      data: []
    }
  },
  watch: {
    show(val) {
      if (val) this.init()
    }
  },
  methods: {
    init() {
      this.request().then(res => {
        this.data = res
      })
    },
    mixinsDrawerHeight(height) {
      this.mixinsTableMaxHeight = `${height - 120}px`
    },
    diaClose() {
      this.visible = false
      this.handleClose()
    },
    selectTab(index) {
      this.selectedTabIndex = index // 更新选中的tab索引
      // getData()
    }
  }
}
</script>
<style lang="less" scoped>
.tab-list {
  display: flex;
  margin-top: 20px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-bottom: 10px;
  border-bottom: 1px solid #e8e8e8;
  overflow-x: scroll;
}

.tab-list__item {
  // flex: 1;
  padding: 0 4px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.active-tab {
  color: #dc1e32;
  font-weight: bold;
  position: relative;

  &::before {
    position: absolute;
    content: '';
    bottom: -10px;
    left: 0;
    right: 0;
    margin: auto;
    height: 2px;
    width: 30px;
    border-radius: 4px;
    background-color: #dc1e32;
  }
}
</style>
