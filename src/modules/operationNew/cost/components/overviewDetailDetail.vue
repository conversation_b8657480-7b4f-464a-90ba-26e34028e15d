<template>
  <div class="overview-detail-container">

    <div class="card-item" v-for="item in tabData">
      <div class="card-header">
        <h3 class="card-title fs24">{{ item.title }}</h3>
      </div>
      <div class="card-content">
        <div class="main-value">
          <span class="value fs48">{{ item.value | toFixed2 }}</span>
          <span v-if="item.showUnit" class="value-unit fs32">%</span>

        </div>
        <div class="target-value">
          <span class="target-label fs20">目标值</span>
          <span class="target-amount">{{ item.target | toFixed2 }}元</span>
        </div>
      </div>
      <div class="card-content card-bottom">
        <div class="ratio-item">
          <span class="ratio-label fs20">{{ completionRatioLabel }}</span>
          <span class="ratio-value fs20">
            {{ item.monthRatio | toFixed2 }}%
          </span>
        </div>
        <div class="ratio-item">
          <span class="ratio-label fs20">{{ chainRatioLabel }}</span>
          <span class="ratio-value fs20">
            {{ item.monthRatioCompelte | toFixed2 }}%
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import baseMixins from '../../baseMixins'

export default {
  name: 'OverviewDetailDetail',
  mixins: [baseMixins],
  filters: {
    // 格式化数值为小数点后两位
    toFixed2(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      const num = parseFloat(value)
      if (isNaN(num)) {
        return '0.00'
      }
      return num.toFixed(2)
    },
    // 格式化数值为小数点后两位
    toFixed1(value) {
      if (value === null || value === undefined || value === '') {
        return '0.0'
      }
      const num = parseFloat(value)
      if (isNaN(num)) {
        return '0.0'
      }
      return num.toFixed(1)
    }
  },
  props: {
    data: {
      type: Object,
      default: () => { }
    },
    dateType: {
      type: Number,
      default: 1 // 1日 2周 3月
    }
  },
  data() {
    return {
      // 是否是省区或总部层级（true: 省区/总部, false: 区域/场站）
      isProvinceOrHeadquarters: false,
      homeCardData: [
        {
          title: '营运单公斤 (元)',
          value: '3.76',
          target: '2.86',
          monthRatio: 86.9,
          monthRatioCompelte: 88.1
        },
        {
          title: '运营单公斤 (元)',
          value: '2.89',
          target: '3.05',
          monthRatio: 94.8,
          monthRatioCompelte: 96.3
        },
        {
          title: 'OD签收货量 (T)',
          value: '4.56',
          target: '5.05',
          monthRatio: 90.3,
          monthRatioCompelte: 92.7
        }
      ],
      areaCardData: [
        {
          title: '干线单公斤(元)',
          value: '2.65',
          target: '3.05',
          monthRatio: 86.9,
          dayRatio: 88.1
        },
        {
          title: '干线单公斤(元)',
          value: '2.65',
          target: '3.05',
          monthRatio: 86.9,
          dayRatio: 88.1
        },
        {
          title: '中转单公斤(元)',
          value: '2.89',
          target: '3.05',
          monthRatio: 94.8,
          dayRatio: 96.3
        },
        {
          title: '收派单公斤(元)',
          value: '4.56',
          target: '5.05',
          monthRatio: 90.3,
          dayRatio: 92.7
        }
      ]
    }
  },
  computed: {
    // 判断是否是省区或总部层级
    // 如果没有areaCode，说明是省区或总部层级
    showHomeData() {

      if (this.zoneLevel == 32 || this.zoneLevel == 30 || this.zoneLevel == 33) {
        return true
      }
      return false
    },
    // 根据日期类型动态生成标签文本
    completionRatioLabel() {
      const labels = {
        1: '日完成比', // 日
        2: '周完成比', // 周
        3: '月完成比'  // 月
      }
      return labels[this.dateType] || '日完成比'
    },
    chainRatioLabel() {
      const labels = {
        1: '日环比', // 日
        2: '周环比', // 周
        3: '月环比'  // 月
      }
      return labels[this.dateType] || '日环比'
    },
    tabData() {
      let ret = []
      const detailData = this.data || []
      if (this.showHomeData) {
        ret = [
          {
            title: '营运单公斤 (元)',
            value: detailData.yyCostPerKg || 0,
            target: detailData.yyCostTarget || 0,
            monthRatio: detailData.yyCostChRatePrev || 0,
            monthRatioCompelte: detailData.yyCostTargetAchRate || 0,
          },
          {
            title: '干线单公斤 (元)',
            value: detailData.gxCostPerKg || 0,
            target: detailData.gxCostTarget || 0,
            monthRatio: detailData.gxCostChRatePrev || 0,
            monthRatioCompelte: detailData.gxCostTargetAchRate || 0,
          },
          {
            title: '中转单公斤 (元)',
            value: detailData.transferCostPerKg || 0,
            target: detailData.transferCostTarget || 0,
            monthRatio: detailData.transferCostChRatePrev || 0,
            monthRatioCompelte: detailData.transferCostTargetAchRate || 0,
          },
          {
            title: '收派单公斤 (元)',
            value: detailData.spCostChRatePrev || 0,
            target: detailData.spCostTarget || 0,
            monthRatio: detailData.transferCostChRatePrev || 0,
            monthRatioCompelte: detailData.spCostTargetAchRate || 0,
          }
        ]
      } else {
        ret = [
          {
            title: '操作货量 (T)',
            value: '3.76',
            target: '2.86',
            monthRatio: 86.9,
            monthRatioCompelte: 88.1
          },
          {
            title: '体积装载率',
            value: '2.89',
            showUnit: true,
            target: '3.05',
            monthRatio: 94.8,
            monthRatioCompelte: 96.3
          },
          {
            title: '在职劳效',
            value: '4.56',
            target: '5.05',
            monthRatio: 90.3,
            monthRatioCompelte: 92.7
          },
          {
            title: '计费重量装载率',
            value: '4.56',
            showUnit: true,
            target: '5.05',
            monthRatio: 90.3,
            monthRatioCompelte: 92.7
          }
        ]
      }
      return ret
    }
  },
  watch: {
    // 监听层级变化
    showHomeData: {
      handler(newVal) {
        this.isProvinceOrHeadquarters = newVal
      },
      immediate: true
    }
  }
}
</script>

<style lang="less" scoped>
.overview-detail-container {
  color: #ffffff;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 0.2rem;

  .home-card-item {
    width: 2.3rem;
    border-radius: 0.08rem;
    background: linear-gradient(174deg, #4F699F 0%, #2E3F63 93%);
    box-shadow: 0px 8px 18px 0px rgba(34, 48, 77, 0.3);
    margin-bottom: 0.07rem;

    .card-content-row-wrapper {
      margin-top: 0.12rem;
      padding-top: 0.09rem;
      padding-bottom: 0.12rem;
      background: rgba(255, 255, 255, 0.05);

      .card-content-row-left {
        font-weight: normal;
        letter-spacing: normal;
        color: rgba(255, 255, 255, 0.7);
        width: 0.96rem;
        margin-right: 0;
      }

      .card-content-row-right {
        font-family: PingFang SC;
        font-size: 0.2rem;
        font-weight: 500;
        letter-spacing: normal;
        color: #FFFFFF;
      }
    }

    .card-content-row-margin {
      margin-bottom: 0.12rem;
    }

    .card-header {
      margin-bottom: 0.24rem;
    }

    .card-content {
      margin-bottom: 0.14rem;
    }

    .card-content-row {
      display: flex;
      align-items: center;
      padding: 0 0.2rem;
      font-size: 0.2rem;

      // margin-bottom: 0.09rem;
    }

    .card-content-row-left {
      margin-right: 0.16rem;

    }

    .card-content-row-right {
      font-family: DINPro;
      font-weight: 700;
      font-size: 0.24rem;
    }
  }

  .card-item {
    // padding: 0.2rem;
    width: 3.5rem;
    border-radius: 0.08rem;
    background: linear-gradient(174deg, #4F699F 0%, #2E3F63 93%);
    box-shadow: 0px 8px 18px 0px rgba(34, 48, 77, 0.3);
    margin-bottom: 0.2rem;
  }

  .card-title {
    color: #FFFFFF;
    padding: 0.15rem 0.15rem 0 0.15rem;
  }

  .card-content {
    padding: 0 0.15rem;
    margin-top: 0.12rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;

    .main-value {
      .value {
        font-weight: bold;
      }
    }

    .target-label {
      color: rgba(255, 255, 255, 0.7);
      margin-right: 0.12rem;
    }

    .ratio-item {
      display: flex;
      align-items: center;
    }

    .ratio-label {
      color: rgba(255, 255, 255, 0.7);
      margin-right: 0.06rem;
    }

    .ratio-value {
      font-weight: 500;
    }
  }

  .card-bottom {
    background: rgba(255, 255, 255, 0.05);
    padding-bottom: 0.12rem;
    padding-top: 0.12rem;
  }
}
</style>