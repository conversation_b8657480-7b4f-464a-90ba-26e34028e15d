<!--
 * @Author: shigl
 * @Date: 2022-11-20 18:07:55
 * @LastEditTime: 2023-03-17 14:32:10
 * @Description: 运营品效总览
-->
<template>
  <CardList title="营运成本总览">
    <OverviewDetailDetail :data="detailData" :dateType="currentDateType"></OverviewDetailDetail>
  </CardList>
</template>
<script>
import request from './commonMixins/request'
import mixins from './commonMixins/mixins'
import { overviewOptionCZ, overviewOption } from './commonMixins/config'
import OverviewDetailDetail from './components/overviewDetailDetail.vue'
import { mapMutations } from 'vuex'
export default {
  components: {
    OverviewDetailDetail
  },
  mixins: [request, mixins],
  props: {
    // 从父组件传入的API参数
    areaCode: {
      type: String,
      default: ''
    },
    dataLevel: {
      type: Number,
      default: 30
    },
    provinceAreaCode: {
      type: String,
      default: ''
    },
    // 从父组件传入的当前日期类型（1=日，2=周，3=月）
    currentDateType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      dataSource: {},
      detailData: {},
      overviewOptionCZ,
      overviewOption
    }
  },
  computed: {
    columns() {
      return this.is_station_level ? this.overviewOptionCZ : this.overviewOption
    }
  },
  watch: {
    // 监听props变化，重新获取数据
    areaCode() {
      this.init()
    },
    dataLevel() {
      this.init()
    },
    provinceAreaCode() {
      this.init()
    },
    dateDay() {
      this.init()
    },
    // 监听父组件传入的日期类型变化
    currentDateType() {
      this.init()
    }
  },
  methods: {
    ...mapMutations('operationNew', ['setDate', 'setDateIndex', 'setPageData']),
    async init() {
      await this.getOperatorQualityData()
    },
    async getOperatorQualityData() {
      // 使用从父组件传入的参数
      const data = {
        areaCode: this.areaCode,
        dataLevel: this.dataLevel,
        dateType: this.currentDateType,
        provinceAreaCode: this.provinceAreaCode,
        date: this.$moment(this.dateValue).format('YYYY-MM-DD')
      }

      const res = await this._getsxzkOperatingCostData(data)
      this.detailData = res.obj || {}
    },
  },
  mounted() {
    this.init()
  }
}
</script>
<style lang='less' scoped></style>
